// nolint:dupl
package abfl

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

const (
	maxAddressWaitTimeHrs = 120
)

// Address represents the stage which needs a user to enter details for address
type Address struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

func NewAddress() *Address {
	return &Address{}
}

var _ stages.IStage = &Address{}

func (ca *Address) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	updateReq := &palActivityPb.UpdateLRNextActionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanRequestId: req.Request.GetLoanStep().GetRefId(),
		NextAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN,
		},
	}

	updateRes := &palActivityPb.PalActivityResponse{}
	updateErr := activityPkg.Execute(ctx, palNs.UpdateLRNextAction, updateRes, updateReq)
	if providers.IsActivityError(ctx, updateRes.GetLoanStep(), nil, res, updateErr, false) {
		return res, updateErr
	}

	addErr := ca.performAddAddressDetails(ctx, req)
	if addErr != nil {
		if epifitemporal.IsRetryableError(addErr) {
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED
		}
		return res, addErr
	}
	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *Address) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS
}

func (ca *Address) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAddress
}

func (ca *Address) performAddAddressDetails(ctx workflow.Context, req *stages.PerformRequest) error {
	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}

	addressConfirmationFuture, err := activityPkg.ExecuteAsync(ctx, palNs.GetAddressStatus, actReq, actRes)
	if err != nil {
		return fmt.Errorf("failed to execute GetAddressStatus activity: %w", err)
	}

	var errorToReturn error
	addressConfirmationFuture.AddFutureHandler(func(futureGetErr error, resp *palActivityPb.PalActivityResponse) {
		if futureGetErr != nil {
			workflow.GetLogger(ctx).Info("address details confirmation future finished with error", zap.Error(futureGetErr))
			errorToReturn = futureGetErr
		}
	})
	signalPayload := &palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	sigChannel := epifitemporal.NewSignalChannel(ctx, palNs.AddressConfirmationSignal, signalPayload)
	sigChannel.AddReceiverHandler(func(getErr error, payload *palActivityPb.LoanApplicationESignVerificationSignalPayload) {
		workflow.GetLogger(ctx).Info("AddressConfirmationSignal signal returned first")
		if getErr != nil {
			workflow.GetLogger(ctx).Info("AddressConfirmationSignal signal processing failed", zap.Error(getErr))
			errorToReturn = getErr
		}
	})

	err = epifitemporal.ReceiveSignalWithFuture(ctx, addressConfirmationFuture, sigChannel, maxAddressWaitTimeHrs*time.Hour)
	if err != nil {
		workflow.GetLogger(ctx).Info("AddressConfirmationSignal receive signal with future processing failed", zap.Error(err))
		return err
	}
	if errorToReturn != nil {
		return errorToReturn
	}

	return nil
}
