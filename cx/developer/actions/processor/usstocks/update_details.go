package usstocks

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/usstocks/catalog"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

const (
	STOCK_ID        = "id"
	STOCK_SYMBOL    = "symbol"
	STOCK_EXCHANGE  = "exchange"
	INTERNAL_STATUS = "internal_status"
	SHORT_NAME      = "short_name"
)

type UpdateDetails struct {
	catalogClient catalog.CatalogManagerClient
}

func NewUSStocksUpdateDetails(catalogClient catalog.CatalogManagerClient) *UpdateDetails {
	return &UpdateDetails{catalogClient: catalogClient}
}

func (s *UpdateDetails) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {

	var InternalStatuses []string
	for _, InternalStatus := range catalog.InternalStatus_name {
		if InternalStatus != catalog.InternalStatus_name[int32(catalog.InternalStatus_INTERNAL_STATUS_UNSPECIFIED)] {
			InternalStatuses = append(InternalStatuses, InternalStatus)
		}
	}

	paramList := []*dsPb.ParameterMeta{
		// Stock Id, Stock Symbol and Stock Exchange cannot be updated
		{
			Name:            STOCK_ID,
			Label:           "Stock Id (Takes Either Stock Id or Symbol+Exchange to update details)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            STOCK_SYMBOL,
			Label:           "Stock Symbol (use with Exchange)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            STOCK_EXCHANGE,
			Label:           "Stock Exchange (use with Symbol)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            INTERNAL_STATUS,
			Label:           "New Internal Status (To be Updated)",
			Type:            dsPb.ParameterDataType_DROPDOWN,
			Options:         InternalStatuses,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
		{
			Name:            SHORT_NAME,
			Label:           "Company Short Name (To be Updated)",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (s *UpdateDetails) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var stockId, stockSymbol, stockExchange, internalStatus, shortName string
	var req *catalog.UpdateStockRequest
	var marshalledRes []byte

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case STOCK_ID:
			stockId = filter.GetStringValue()
		case STOCK_SYMBOL:
			stockSymbol = filter.GetStringValue()
		case STOCK_EXCHANGE:
			stockExchange = filter.GetStringValue()
		case INTERNAL_STATUS:
			internalStatus = filter.GetDropdownValue()
		case SHORT_NAME:
			shortName = filter.GetStringValue()
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	if internalStatus == "" && shortName == "" {
		return "", status.Error(codes.InvalidArgument, "at least one of internal status or short name must be provided")
	}

	// Build the stock object and field mask based on what needs to be updated
	var stockToBeUpdated *catalog.Stock
	var fieldPaths []string

	switch {
	case stockId != "":
		stockToBeUpdated = &catalog.Stock{
			Id: stockId,
		}

	case stockSymbol != "" && stockExchange != "":
		stockToBeUpdated = &catalog.Stock{
			Symbol:   stockSymbol,
			Exchange: catalog.Exchange(catalog.Exchange_value[stockExchange]),
		}

	default:
		logger.Error(ctx, fmt.Sprintf("stockId %s, stockSymbol %s and stockExchange %s cannot be empty", stockId, stockSymbol, stockExchange))
		return "", status.Error(codes.InvalidArgument, "stockId or (stockSymbol and stockExchange) cannot be empty")
	}

	if internalStatus != "" {
		stockToBeUpdated.InternalStatus = catalog.InternalStatus(catalog.InternalStatus_value[internalStatus])
		fieldPaths = append(fieldPaths, INTERNAL_STATUS)
	}

	if shortName != "" {
		stockToBeUpdated.CompanyInfo = &catalog.CompanyInfo{
			CompanyName: &catalog.CompanyName{
				ShortName: shortName,
			},
		}
		fieldPaths = append(fieldPaths, "company_info.company_name.short_name")
	}

	req = &catalog.UpdateStockRequest{
		Stock:     stockToBeUpdated,
		FieldMask: &fieldmaskpb.FieldMask{Paths: fieldPaths},
	}

	res, err := s.catalogClient.UpdateStock(ctx, req)

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in update stock operation", zap.Error(rpcErr))
		return "", rpcErr
	}

	marshalledRes, err = protojson.Marshal(res)

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error marshalling to json: %v", res))
		marshalledRes = []byte(fmt.Sprintf("error marshalling to json: %v", res))
		return string(marshalledRes), nil
	}

	return string(marshalledRes), nil
}
