package chat

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/risk/enums"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	helper2 "github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/risk/accountstatus"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	chatPb "github.com/epifi/gamma/api/cx/chat"
	consumerPb "github.com/epifi/gamma/api/cx/consumer"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/cx/chat/dao"
	"github.com/epifi/gamma/cx/chat/helper"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/metrics"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/epifi/gamma/api/cx/ticket"
	userPb "github.com/epifi/gamma/api/user"

	cxEvents "github.com/epifi/gamma/cx/events"
)

const (
	ticketSourcePhone = "Phone"
	ticketSourceEmail = "Email"
	// context code for fi-lite users
	ContextCodeFiLiteUser = "FI_LITE"
	// context code for directly connecting user to an agent
	ContextCodeConnectToAnAgent      = "Connect me with an agent"
	userId                           = "cp_user_id"
	TxnId                            = "TXN_ID"
	authKey                          = "cp_auth_token"
	AccountFreezeUsersFreshchatTopic = "account_freeze_users"
	BotType                          = "cp_flow_type"
	UserID                           = "user_id"
	accountFreezeBot                 = "account_freeze"
	transactionBot                   = "transactions"
	NuggetBot                        = "NuggetBot"
	nuggetTxnId                      = "cp_txn_id"
	NuggetPropertyKeyPrefix          = "cp_"
)

type Service struct {
	updateEventDAO       dao.IFreshdeskUpdateEventDAO
	idGen                idgen.IdGenerator
	ticketPub            cxTypes.FreshdeskTicketPublisher
	vendorMappingClient  vendormapping.VendorMappingServiceClient
	genConf              *cxGenConf.Config
	authClient           authPb.AuthClient
	actorClient          actorPb.ActorClient
	userClient           userPb.UsersClient
	releaseEvaluator     release.IEvaluator
	fcUserMappingHelper  helper.IFreshchatUserMappingHelper
	onboardingClient     onboarding.OnboardingClient
	conf                 *config.Config
	fdClient             freshdeskPb.FreshdeskClient
	accountStatusFetcher accountstatus.Fetcher
	eventBroker          events.Broker
	customerIdentifier   helper2.ICustomerIdentifier
	vgNuggetClient       nugget.NuggetChatbotServiceClient
}

func NewService(idGen idgen.IdGenerator, ticketPub cxTypes.FreshdeskTicketPublisher, updateEventDAO dao.IFreshdeskUpdateEventDAO,
	vendorMappingClient vendormapping.VendorMappingServiceClient, genConf *cxGenConf.Config,
	authClient authPb.AuthClient, actorClient actorPb.ActorClient, userClient userPb.UsersClient,
	releaseEvaluator release.IEvaluator, fcUserMappingHelper helper.IFreshchatUserMappingHelper,
	onboardingClient onboarding.OnboardingClient, conf *config.Config, accountStatusFetcher accountstatus.Fetcher,
	eventBroker events.Broker, fdClient freshdeskPb.FreshdeskClient, customerIdentifier helper2.ICustomerIdentifier,
	vgNuggetClient nugget.NuggetChatbotServiceClient) *Service {
	return &Service{
		idGen:                idGen,
		ticketPub:            ticketPub,
		updateEventDAO:       updateEventDAO,
		vendorMappingClient:  vendorMappingClient,
		genConf:              genConf,
		authClient:           authClient,
		actorClient:          actorClient,
		userClient:           userClient,
		releaseEvaluator:     releaseEvaluator,
		fcUserMappingHelper:  fcUserMappingHelper,
		onboardingClient:     onboardingClient,
		conf:                 conf,
		accountStatusFetcher: accountStatusFetcher,
		eventBroker:          eventBroker,
		fdClient:             fdClient,
		customerIdentifier:   customerIdentifier,
		vgNuggetClient:       vgNuggetClient,
	}
}

var _ chatPb.ChatsServer = &Service{}

func (s *Service) GetChatInitInformationForActor(ctx context.Context, req *chatPb.GetChatInitInformationForActorRequest) (*chatPb.GetChatInitInformationForActorResponse, error) {
	if req.GetActorId() == "" {
		cxLogger.Info(ctx, "actor id not present in get chat init information for actor request")
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id is mandatory"),
		}, nil
	}

	// build appropriate chat init response
	return s.BuildChatInitResponse(ctx, req)
}

func (s *Service) BuildChatInitResponse(ctx context.Context, req *chatPb.GetChatInitInformationForActorRequest) (*chatPb.GetChatInitInformationForActorResponse, error) {
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)
	failureInfo := req.GetClientSideChatFailureInfo()
	failureCount := failureInfo.GetFailureCount()
	metrics.RecordChatInitRequest(appPlatform, failureInfo.GetLastTriedChatView(), failureInfo.GetFailureCount(),
		req.GetClientSideChatFailureInfo().GetFailureReason(), req.GetForceNewSession())
	if s.genConf.ChatBotConfig().IsExtraLoggingEnabled() {
		logger.Error(ctx, "GetChatInitInformationForActorRequest", zap.Any("ClientSideChatFailureInfo", failureInfo),
			zap.String("LastSuccessfullyLoadedChatView", req.GetLastSuccessfullyLoadedChatView().String()), zap.Any("LastSuccessfulSessionTime", req.GetLastSuccessfulSessionTime().AsTime().In(datetime.IST)),
			zap.Any("ForceNewSession", req.GetForceNewSession()))
	}
	// if client side failure count has is in multiples of configured threshold then return auto retry as false
	// we are doing this mod operation to compensate for any client side bug which fails to reset failure count at client side
	// this gives headroom for auto retries in case failure count counter is not properly managed on client side
	if failureCount != 0 {
		// logging the entire request as we dont have any sensitive information. This would help in better debugging
		logger.Error(ctx, "Client side failure for chat initialization", zap.Any("ClientSideChatFailureInfo", failureInfo),
			zap.String("LastSuccessfullyLoadedChatView", req.GetLastSuccessfullyLoadedChatView().String()), zap.Any("LastSuccessfulSessionTime", req.GetLastSuccessfulSessionTime().AsTime().In(datetime.IST)),
			zap.Any("ForceNewSession", req.GetForceNewSession()))
		// Return permanent failure with auto retry as false if the failure count reaches a threshold
		// using modulo here to avoid infinite auto retry in case the client misses resetting the failure count
		if (failureCount % s.genConf.ChatBotConfig().MaxClientSideFailureCountAllowedForAutoRetry()) == 0 {
			shouldAutoRetry := commontypes.BooleanEnum_FALSE
			metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, shouldAutoRetry, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
			return &chatPb.GetChatInitInformationForActorResponse{
				Status:          rpcPb.StatusPermanentFailure(),
				ShouldAutoRetry: shouldAutoRetry,
			}, nil
		}
	}
	generateNewSessionData := req.GetForceNewSession()

	// Forcing nugget chatbot for testing
	if isEligible, ok := s.genConf.ChatBotConfig().ActorIdsEnabledForForceNuggetChatbot().Load(req.GetActorId()); ok && isEligible {
		return s.getChatInitInformationByChatView(ctx, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK.String(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
	}

	// If user came from either of entry points where nugget is configured then showing nugget chatbot
	if req.GetMetadata() != nil {
		if _, exists := req.GetMetadata()[NuggetBot]; exists {
			return s.getChatInitInformationByChatView(ctx, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK.String(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
		}
	}

	// if last loaded chat view is not unspecified
	// and last successful chat session time is within configured threshold
	// then return the view which user has last interacted with
	if req.GetLastSuccessfullyLoadedChatView() != types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED && time.Now().Sub(req.GetLastSuccessfulSessionTime().AsTime()) <= s.genConf.ChatBotConfig().MaxTimeDurationThresholdForLastSuccessfulSessionTime() {
		return s.getChatInitInformationByChatView(ctx, req.GetLastSuccessfullyLoadedChatView().String(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
	}
	// if the time duration is above the threshold as checked above, we would create new session data by default
	generateNewSessionData = commontypes.BooleanEnum_TRUE
	// if force fallback is enabled then load default in app chat view configured in config
	if s.genConf.ChatBotConfig().IsForceFallbackToDefaultEnabled() {
		return s.getChatInitInformationByChatView(ctx, s.genConf.ChatBotConfig().DefaultInAppChatView(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
	}

	// Why we are doing this release evaluation check:
	// This determines which chatbot interface should be shown to the user based on feature flags
	// TODO: Move the evaluation logic for determining which chatbot should open to a helper function to reduce responsibility on the current function,
	// Add events & metrics so that we can evaluate/calculate users getting redirected to different chatbot setup
	if s.genConf.ChatBotConfig().IsFreshChatExperimentEnabled() {
		logger.Info(ctx, "checking if freshchat experiment is enabled for user", zap.Any(logger.ACTOR_ID, req.GetActorId()))
		isFreshChatExperimentEnabledForUser, releaseErr := s.releaseEvaluator.Evaluate(ctx,
			release.NewCommonConstraintData(types.Feature_FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED).WithActorId(req.GetActorId()))

		if releaseErr != nil {
			logger.Error(ctx, "error while evaluating if actor is eligible for freshchat experiment", zap.Error(releaseErr), zap.Any(logger.ACTOR_ID, req.GetActorId()))
			if req.GetLastSuccessfullyLoadedChatView() != types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED {
				return s.getChatInitInformationByChatView(ctx, req.GetLastSuccessfullyLoadedChatView().String(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
			}
			return s.getChatInitInformationByChatView(ctx, s.genConf.ChatBotConfig().DefaultInAppChatView(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
		}

		if isFreshChatExperimentEnabledForUser {
			logger.Info(ctx, "actor is eligible for freshchat experiment", zap.Any(logger.ACTOR_ID, req.GetActorId()))
			resp, err := s.getChatInitInformationForFreshChat(ctx, req.GetActorId())

			if te := epifigrpc.RPCError(resp, err); te != nil {
				logger.Info(ctx, "error while getting freshchat resp in freshchat experiment flow", zap.Error(te), zap.Any(logger.ACTOR_ID, req.GetActorId()))
				return resp, err
			}

			if resp != nil && lo.Contains(s.genConf.ChatBotConfig().ActorIdsEnabledFreshchatIssueTreeExperiment().ToStringArray(), req.GetActorId()) {
				logger.Info(ctx, "actor is eligible for freshchat issue tree experiment", zap.Any(logger.ACTOR_ID, req.GetActorId()))
				resp.TopicTags = []string{AccountFreezeUsersFreshchatTopic}
			}
			return resp, err
		}
	}

	// if release evaluation is enabled then only check for senseforth
	if s.genConf.ChatBotConfig().IsReleaseEvaluationEnabled() {
		// check if given actor is eligible for senseforth chatbot view
		isSenseforthChatBotEnabledForUser, releaseErr := s.releaseEvaluator.Evaluate(ctx,
			release.NewCommonConstraintData(types.Feature_SENSEFORTH_CHATBOT).WithActorId(req.GetActorId()))
		// if error is encountered while checking eligibility for an actor
		// then check if last successfully loaded chat view is not unspecified, if not then return that view
		// if unspecified then return default chat view from config
		if releaseErr != nil {
			logger.Error(ctx, "error while evaluating if actor is eligible for senseforth chatbot", zap.Error(releaseErr), zap.Any(logger.ACTOR_ID, req.GetActorId()))
			if req.GetLastSuccessfullyLoadedChatView() != types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED {
				return s.getChatInitInformationByChatView(ctx, req.GetLastSuccessfullyLoadedChatView().String(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
			}
			return s.getChatInitInformationByChatView(ctx, s.genConf.ChatBotConfig().DefaultInAppChatView(), req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode(), req.GetMetadata(), req.GetScreenMetadata())
		}
		logger.Debug(ctx, "is actor eligible for senseforth", zap.Any("eligibility", isSenseforthChatBotEnabledForUser), zap.Any(logger.ACTOR_ID, req.GetActorId()))
		// if actor is not eligible for senseforth chatbot fetaure, return freshchat init info to client
		if isSenseforthChatBotEnabledForUser {
			return s.getChatInitInformationForSenseforthChatbot(ctx, req.GetActorId(), generateNewSessionData, req.GetDevice(), req.GetSenseforthBotContextCode())
		}
	}

	// else return senseforth chat init info
	return s.getChatInitInformationForFreshChat(ctx, req.GetActorId())
}

func (s *Service) getChatInitInformationByChatView(ctx context.Context, chatView string, actorId string, generateNewSessionData commontypes.BooleanEnum, device *commontypes.Device, botContextCode string, metaData map[string]string, screenMetaData map[string]string) (*chatPb.GetChatInitInformationForActorResponse, error) {
	switch chatView {
	case types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK.String():
		return s.getChatInitInformationForFreshChat(ctx, actorId)
	case types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW.String():
		return s.getChatInitInformationForSenseforthChatbot(ctx, actorId, generateNewSessionData, device, botContextCode)
	case types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK.String():
		return s.getChatInitInformationForNuggetChatbot(ctx, actorId, device, metaData, screenMetaData)
	default:
		logger.Error(ctx, "invalid chat view found for an actor", zap.Any("chatView", chatView))
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
}

func (s *Service) getChatInitInformationForNuggetChatbot(ctx context.Context, actorId string, device *commontypes.Device, metaData map[string]string, screenMetaData map[string]string) (*chatPb.GetChatInitInformationForActorResponse, error) {
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)

	nuggetResp, err := s.FetchAccessTokenForNuggetChatbot(ctx, &chatPb.FetchAccessTokenForNuggetChatbotRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(nuggetResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching access token from nugget", zap.Error(te))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching access token from nugget"),
		}, nil
	}

	// fetch mapping from vendor mapping service
	resp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormapping.GetBEMappingByIdRequest{
		Id: actorId,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching mapping from vendor mapping service", zap.Error(err))
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching mapping from vendor mapping service"),
		}, nil
	}
	shouldAutoRetry := commontypes.BooleanEnum_TRUE
	metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK, shouldAutoRetry, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)

	authResp, authErr := s.authClient.CreateToken(ctx, &authPb.CreateTokenRequest{
		ActorId:   resp.GetZomatoId(),
		TokenType: authPb.TokenType_NUGGET_CHATBOT_ACCESS_TOKEN,
		Device:    device,
	})

	if te := epifigrpc.RPCError(authResp, authErr); te != nil {
		logger.Error(ctx, "error while creating chatbot auth access token", zap.Error(te))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	botProperties := map[string]*types.NuggetChatbotInitInformation_BusinessContext_StringList{
		userId: {
			Values: []string{resp.GetZomatoId()},
		},
		authKey: {
			Values: []string{authResp.GetToken()},
		},
		BotType: {
			Values: []string{accountFreezeBot},
		},
	}

	if val, exists := metaData[TxnId]; exists {
		botProperties[BotType] = &types.NuggetChatbotInitInformation_BusinessContext_StringList{
			Values: []string{transactionBot},
		}
		botProperties[nuggetTxnId] = &types.NuggetChatbotInitInformation_BusinessContext_StringList{
			Values: []string{val},
		}
	}

	if val, exists := metaData[BotType]; exists {
		botProperties[BotType] = &types.NuggetChatbotInitInformation_BusinessContext_StringList{
			Values: []string{val},
		}
		// if bot type is coming from metadata, append other key value pairs also to bot properties which may contain data specific to the entry point from where app is initiating nugget SDK
		for k, v := range metaData {
			// all bot property keys have to be lower case and have cp_ as prefix, this is a constraint of the nugget tool
			k = strings.ToLower(k)
			if !strings.HasPrefix(k, NuggetPropertyKeyPrefix) {
				k = NuggetPropertyKeyPrefix + k
			}
			botProperties[k] = &types.NuggetChatbotInitInformation_BusinessContext_StringList{
				Values: []string{v},
			}
		}
	}

	for k, v := range screenMetaData {
		botProperties[k] = &types.NuggetChatbotInitInformation_BusinessContext_StringList{
			Values: []string{v},
		}
	}

	return &chatPb.GetChatInitInformationForActorResponse{
		Status:             rpcPb.StatusOk(),
		ChatViewToBeLoaded: types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK,
		ChatbotInitInformation: &types.ChatbotInitInformation{
			ChatbotSpecificInitInformation: &types.ChatbotInitInformation_NuggetChatbotInitInformation{
				NuggetChatbotInitInformation: &types.NuggetChatbotInitInformation{
					AccessToken: nuggetResp.GetAccessToken(),
					BusinessContext: &types.NuggetChatbotInitInformation_BusinessContext{
						BotProperties: botProperties,
					},
					Namespace:   s.genConf.ChatBotConfig().NuggetNamespace(),
					DeeplinkUri: s.genConf.ChatBotConfig().NuggetDeeplinkUri(),
					HttpCode:    200,
				},
			},
		},
		ShouldAutoRetry: shouldAutoRetry,
	}, nil
}

func (s *Service) getChatInitInformationForFreshChat(ctx context.Context, actorId string) (*chatPb.GetChatInitInformationForActorResponse, error) {
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)

	freshChatAppId := s.genConf.Secrets().Ids[config.FreshchatAppId]
	freshChatAppKey := s.genConf.Secrets().Ids[config.FreshchatAppKey]
	freshChatAppDomain := s.conf.FreshChatConfig.FreshChatDomain
	if freshChatAppId == "" || freshChatAppKey == "" || freshChatAppDomain == "" {
		cxLogger.Error(ctx, "could not load sdk values from secret manager")
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("could not load sdk values from secret manager"),
		}, nil
	}

	// fetch mapping from vendor mapping service
	resp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormapping.GetBEMappingByIdRequest{
		Id: actorId,
	})

	// error returned by rpc
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching mapping from vendor mapping service", zap.Error(err),
			zap.String(logger.ACTOR_ID, actorId))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching mapping from vendor mapping service"),
		}, nil
	}
	shouldAutoRetry := commontypes.BooleanEnum_TRUE
	metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK, shouldAutoRetry, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
	return &chatPb.GetChatInitInformationForActorResponse{
		Status:               rpcPb.StatusOk(),
		ReferenceId:          resp.GetFreshdeskId(),
		AppId:                freshChatAppId,
		AppKey:               freshChatAppKey,
		Domain:               freshChatAppDomain,
		CustomUserProperties: s.fcUserMappingHelper.GetCustomUserPropertiesMap(ctx, actorId),
		// populate <NAME_EMAIL>, here ref id would be user's freshdesk id
		// we are doing this to make email on freshchat conversations deterministic
		Email:              fmt.Sprintf(s.genConf.FreshChatConfig().FreshChatCustomUserEmailFormat, resp.GetFreshdeskId()),
		ChatViewToBeLoaded: types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK,
		// set should auto retry as true
		ShouldAutoRetry: shouldAutoRetry,
	}, nil
}

//nolint:funlen
func (s *Service) getChatInitInformationForSenseforthChatbot(ctx context.Context, actorId string,
	generateNewSessionData commontypes.BooleanEnum, device *commontypes.Device,
	botContextCode string) (*chatPb.GetChatInitInformationForActorResponse, error) {
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)

	// set the default values in ok-response [Senseforth init info will be populated to this later]
	okResp := &chatPb.GetChatInitInformationForActorResponse{
		Status:             rpcPb.StatusOk(),
		ChatViewToBeLoaded: types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
		// set should auto retry as true
		ShouldAutoRetry: commontypes.BooleanEnum_TRUE,
	}
	botContextCode, err := s.getBotContextCodeForActor(ctx, actorId, botContextCode)
	if err != nil {
		logger.Error(ctx, "error while getting bot context in senseforth chatbot", zap.Error(err), zap.Any(logger.ACTOR_ID, actorId))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	metrics.RecordChatbotContextCodeMetric(botContextCode)
	// enrich ok-response with freshchat init info
	// this is to avoid any app version bug which might happen and break backward compatibility
	// this should be removed when we completely move to senseforth chatbot in prod
	freshchatResp, freshchatErr := s.getChatInitInformationForFreshChat(ctx, actorId)
	if te := epifigrpc.RPCError(freshchatResp, freshchatErr); te != nil {
		logger.Error(ctx, "error while getting freshchat resp in senseforth flow", zap.Error(te), zap.Any(logger.ACTOR_ID, actorId))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	s.enrichChatInitWithFreshchatInfo(okResp, freshchatResp)

	// if we do not need to generate new session data, ask the client to use cached init data
	// This is to persist the bot session [Senseforth will map short token with session id. So creating new token will invalidate the ongoing bot session]
	if generateNewSessionData == commontypes.BooleanEnum_FALSE {
		okResp.SenseforthChatInitInformation = &chatPb.SenseforthChatInitInformation{
			WebViewUrl:     s.genConf.ChatBotConfig().SenseforthChatInitInfo().WebViewURLMap().Get(epificontext.AppPlatformFromContext(ctx).String()),
			ReuseCacheData: commontypes.BooleanEnum_TRUE,
			BotContextCode: botContextCode,
		}
		metrics.RecordChatInitResponse(appPlatform, okResp.GetChatViewToBeLoaded(), okResp.GetShouldAutoRetry(), okResp.GetSenseforthChatInitInformation().GetReuseCacheData())
		logger.Info(ctx, "chatbot access token is not generated", zap.String("ReuseCacheData", okResp.GetSenseforthChatInitInformation().GetReuseCacheData().String()),
			zap.String("ShouldAutoRetry", okResp.GetShouldAutoRetry().String()))
		return okResp, nil
	}
	// get actor object from actor service
	actorResp, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, actorErr); te != nil {
		logger.Error(ctx, "error while getting actor by id", zap.Error(te), zap.Any(logger.ACTOR_ID, actorId))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// create chatbot auth token
	authResp, authErr := s.authClient.CreateToken(ctx, &authPb.CreateTokenRequest{
		ActorId:   actorId,
		TokenType: authPb.TokenType_CHATBOT_ACCESS_TOKEN,
		Device:    device,
	})
	if te := epifigrpc.RPCError(authResp, authErr); te != nil {
		logger.Error(ctx, "error while creating chatbot auth access token for actor", zap.Error(te), zap.Any(logger.ACTOR_ID, actorId))
		metrics.RecordChatInitResponse(appPlatform, types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED)
		return &chatPb.GetChatInitInformationForActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	// set short token and webview url in response
	okResp.SenseforthChatInitInformation = &chatPb.SenseforthChatInitInformation{
		WebViewUrl:     s.genConf.ChatBotConfig().SenseforthChatInitInfo().WebViewURLMap().Get(epificontext.AppPlatformFromContext(ctx).String()),
		ShortToken:     authResp.GetToken(),
		ReuseCacheData: commontypes.BooleanEnum_FALSE,
		BotContextCode: botContextCode,
	}
	// todo: pls change to debug once prod debugging is done
	logger.Info(ctx, "successfully generated chatbot access token", zap.String("ReuseCacheData", okResp.GetSenseforthChatInitInformation().GetReuseCacheData().String()),
		zap.String("ShouldAutoRetry", okResp.GetShouldAutoRetry().String()))
	metrics.RecordChatInitResponse(appPlatform, okResp.GetChatViewToBeLoaded(), okResp.GetShouldAutoRetry(), okResp.GetSenseforthChatInitInformation().GetReuseCacheData())
	return okResp, nil
}

func (s *Service) getBotContextCodeForActor(ctx context.Context, actorId, clientProvidedBotContextCode string) (string, error) {
	// if a context code is pre-populated by client, we don't need to perform check of Fi-Lite user hence skipping it
	if clientProvidedBotContextCode != "" && s.genConf.ChatBotConfig().IsContextCodePassingFromClientEnabled() {
		return clientProvidedBotContextCode, nil
	}
	botContextCode := ""
	fiLiteResp, fiLiteErr := s.onboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onboarding.Feature_FEATURE_FI_LITE,
	})
	if te := epifigrpc.RPCError(fiLiteResp, fiLiteErr); te != nil {
		cxLogger.Error(ctx, "error while fetching fi lite details for user", zap.Error(te), zap.String(logger.ACTOR_ID, actorId))
		return "", errors.Wrap(te, "failed to check if fi lite user")
	}
	if fiLiteResp.GetIsFiLiteUser() {
		// Set context code for Fi Lite users, so that a different menu will be shown on the bot
		botContextCode = ContextCodeFiLiteUser
	}
	// For non-Fi Lite users, context code is empty so that the usual flows are shown
	// todo: pls change to debug once prod debugging is done
	logger.Info(ctx, "bot context code for invoking senseforth chatbot", zap.String("botContextCode", botContextCode))
	return botContextCode, nil
}

func (s *Service) enrichChatInitWithFreshchatInfo(senseforthResp *chatPb.GetChatInitInformationForActorResponse, freshchatResp *chatPb.GetChatInitInformationForActorResponse) {
	senseforthResp.ReferenceId = freshchatResp.GetReferenceId()
	senseforthResp.AppId = freshchatResp.GetAppId()
	senseforthResp.AppKey = freshchatResp.GetAppKey()
	senseforthResp.Domain = freshchatResp.GetDomain()
	senseforthResp.CustomUserProperties = freshchatResp.GetCustomUserProperties()
	senseforthResp.Email = freshchatResp.GetEmail()
}

func (s *Service) GetReferenceIdForActor(ctx context.Context, req *chatPb.GetReferenceIdForActorRequest) (*chatPb.GetReferenceIdForActorResponse, error) {
	if req.GetActorId() == "" {
		cxLogger.Info(ctx, "actor id not present in get reference id for actor")
		return &chatPb.GetReferenceIdForActorResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor id is mandatory"),
		}, nil
	}
	freshchatAppId := s.genConf.Secrets().Ids[config.FreshchatAppId]
	freshchatAppKey := s.genConf.Secrets().Ids[config.FreshchatAppKey]
	freshchatDomain := s.conf.FreshChatConfig.FreshChatDomain
	if freshchatAppId == "" || freshchatAppKey == "" || freshchatDomain == "" {
		cxLogger.Error(ctx, "could not load sdk values from sm")
		return &chatPb.GetReferenceIdForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("could not load sdk values from sm"),
		}, nil
	}
	// fetch mapping from vendor mapping service
	resp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormapping.GetBEMappingByIdRequest{
		Id: req.GetActorId(),
	})

	// error returned by rpc
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching mapping from vendor mapping service", zap.Error(err),
			zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &chatPb.GetReferenceIdForActorResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &chatPb.GetReferenceIdForActorResponse{
		Status:      rpcPb.StatusOk(),
		ActorId:     req.GetActorId(),
		ReferenceId: resp.GetFreshdeskId(),
		AppId:       freshchatAppId,
		AppKey:      freshchatAppKey,
		Domain:      freshchatDomain,
	}, nil
}

// This method is idempotent in the sense that everytime this gets called it will push the message to queue
func (s *Service) UpdateTicketForChat(ctx context.Context, req *chatPb.UpdateTicketForChatRequest) (*chatPb.UpdateTicketForChatResponse, error) {
	cxLogger.Info(ctx, "received event for updating ticket for chat", zap.Int64(logger.TICKET_ID, req.GetTicketId()),
		zap.String("reference_id", req.GetReferenceId()))
	if req.GetTicketId() == 0 || req.GetReferenceId() == "" {
		return &chatPb.UpdateTicketForChatResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id and reference id are mandatory params"),
		}, nil
	}
	// create entry in update events table and then push the message to queue
	// If the record already exists for this ticket id error is not thrown and message is pushed to queue
	// since our end goal is to update ticket in freshdesk
	msg, err := s.updateEventDAO.Create(ctx, req.GetTicketId(), consumerPb.EventType_TICKET)
	if err != nil {
		cxLogger.Error(ctx, "error in creating db entry for updating ref id in ticket",
			zap.Int64(logger.TICKET_ID, req.GetTicketId()), zap.Error(err))
		return &chatPb.UpdateTicketForChatResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	sqsId, err := s.ticketPub.Publish(ctx, &consumerPb.ProcessTicketEventRequest{
		TicketId:    req.GetTicketId(),
		ReferenceId: req.GetReferenceId(),
		TicketEvent: consumerPb.TicketEvent_UPDATE_REQUESTER_DETAILS,
		MessageId:   msg.Id,
	})
	if err != nil {
		cxLogger.Error(ctx, "error in publishing event to queue for updating ref id in ticket",
			zap.Int64(logger.TICKET_ID, req.GetTicketId()), zap.Error(err))
		return &chatPb.UpdateTicketForChatResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	cxLogger.Info(ctx, "request event log", zap.String(logger.QUEUE_MESSAGE_ID, sqsId), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
	return &chatPb.UpdateTicketForChatResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) GetActorIdFromReferenceId(ctx context.Context, req *chatPb.GetActorIdFromReferenceIdRequest) (*chatPb.GetActorIdFromReferenceIdResponse, error) {
	if req.GetReferenceId() == "" {
		return &chatPb.GetActorIdFromReferenceIdResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("reference id not passed in request"),
		}, nil
	}

	// First try Freshdesk vendor
	resp, err := s.vendorMappingClient.GetInputIdByVendor(ctx, &vendormapping.GetInputIdByVendorRequest{
		Id:     req.GetReferenceId(),
		Vendor: commonvgpb.Vendor_FRESHDESK,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		if err == nil && resp.GetStatus().IsRecordNotFound() {
			// If Freshdesk mapping not found, try Zomato vendor
			cxLogger.Info(ctx, "Freshdesk mapping not found, trying Zomato vendor", zap.String(logger.REFERENCE_ID, req.GetReferenceId()))
			resp, err = s.vendorMappingClient.GetInputIdByVendor(ctx, &vendormapping.GetInputIdByVendorRequest{
				Id:     req.GetReferenceId(),
				Vendor: commonvgpb.Vendor_ZOMATO,
			})
			if te := epifigrpc.RPCError(resp, err); te != nil {
				if err == nil && resp.GetStatus().IsRecordNotFound() {
					cxLogger.Info(ctx, "neither Freshdesk nor Zomato mapping found for ref id", zap.String(logger.REFERENCE_ID, req.GetReferenceId()))
					return &chatPb.GetActorIdFromReferenceIdResponse{
						Status: rpcPb.StatusRecordNotFoundWithDebugMsg("mapping not found for actor id and reference id"),
					}, nil
				}
				cxLogger.Error(ctx, "error getting ref id to actor id mapping from Zomato vendor", zap.Error(te),
					zap.String(logger.REFERENCE_ID, req.GetReferenceId()))
				return &chatPb.GetActorIdFromReferenceIdResponse{
					Status: rpcPb.StatusInternalWithDebugMsg("internal error in getting mapping for ref id to actor id"),
				}, nil
			}
			return &chatPb.GetActorIdFromReferenceIdResponse{
				Status:  rpcPb.StatusOk(),
				ActorId: resp.GetInputId(),
			}, nil
		}
		cxLogger.Error(ctx, "error getting ref id to actor id mapping from Freshdesk vendor", zap.Error(te),
			zap.String(logger.REFERENCE_ID, req.GetReferenceId()))
		return &chatPb.GetActorIdFromReferenceIdResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("internal error in getting mapping for ref id to actor id"),
		}, nil
	}

	// Freshdesk mapping found
	return &chatPb.GetActorIdFromReferenceIdResponse{
		Status:  rpcPb.StatusOk(),
		ActorId: resp.GetInputId(),
	}, nil
}

func (s *Service) UpdateTicketForSource(ctx context.Context, req *chatPb.UpdateTicketForSourceRequest) (*chatPb.UpdateTicketForSourceResponse, error) {
	cxLogger.Info(ctx, "received event for updating ticket", zap.Int64(logger.TICKET_ID, req.GetTicketId()))
	if req.GetTicketId() == 0 || (req.GetPhoneNumber() == "" && req.GetEmail() == "") {
		return &chatPb.UpdateTicketForSourceResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id and phone number or emails are mandatory params"),
		}, nil
	}

	source := req.GetSource()
	if source == ticketSourceEmail {
		s.handleRiskOpsRerouteIfNeeded(ctx, req)
	}

	// create entry in update events table and then push the message to queue
	// If the record already exists for this ticket id error is not thrown and message is pushed to queue
	// since our end goal is to update ticket in freshdesk
	msg, err := s.updateEventDAO.Create(ctx, req.GetTicketId(), consumerPb.EventType_TICKET)
	if err != nil {
		cxLogger.Error(ctx, "error in creating db entry for updating ref id in ticket",
			zap.Int64(logger.TICKET_ID, req.GetTicketId()), zap.Error(err))
		return &chatPb.UpdateTicketForSourceResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	eventRequest := &consumerPb.ProcessTicketEventRequest{
		TicketId:    req.GetTicketId(),
		Email:       req.GetEmail(),
		TicketEvent: consumerPb.TicketEvent_UPDATE_REQUESTER_DETAILS_FOR_SOURCE,
		MessageId:   msg.Id,
	}
	if req.GetPhoneNumber() != "" {
		parsedPhoneNumber, phoneErr := commontypes.ParsePhoneNumber(req.GetPhoneNumber())
		if phoneErr != nil {
			cxLogger.Error(ctx, "failed to parse phone number in request",
				zap.Int64(logger.TICKET_ID, req.GetTicketId()), zap.Error(phoneErr))
		} else {
			if parsedPhoneNumber.CountryCode == 0 {
				parsedPhoneNumber.CountryCode = 91
			}
			eventRequest.PhoneNumber = parsedPhoneNumber
		}
	}
	// if phone number if not an Indian number we can skip this particular ticket since we won't find any user with
	// a non-indian phone number internally
	if req.GetSource() == ticketSourcePhone && eventRequest.GetPhoneNumber().GetCountryCode() != 91 {
		logger.Info(ctx, "skipping publishing event for the ticket since phone number is non-indian number",
			zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return &chatPb.UpdateTicketForSourceResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}
	sqsId, err := s.ticketPub.Publish(ctx, eventRequest)
	if err != nil {
		cxLogger.Error(ctx, "error in publishing event to queue for updating ref id in ticket",
			zap.Int64(logger.TICKET_ID, req.GetTicketId()), zap.Error(err))
		return &chatPb.UpdateTicketForSourceResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in publishing event to queue for updating ref id in ticket"),
		}, nil
	}
	cxLogger.Info(ctx, "request event log", zap.String(logger.QUEUE_MESSAGE_ID, sqsId), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
	return &chatPb.UpdateTicketForSourceResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// handleRiskOpsRerouteIfNeeded encapsulates all risk-blocked/reroute logic. Returns nil if rerouted/ not rerouted, or err on error.
func (s *Service) handleRiskOpsRerouteIfNeeded(ctx context.Context, req *chatPb.UpdateTicketForSourceRequest) {
	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_EmailId{
			EmailId: req.GetEmail(),
		},
	})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		if userRes == nil || !userRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error getting user by email", zap.Error(te))
		}
		return
	}

	actorRes, err := s.actorClient.GetActorByEntityId(ctx, &actorPb.GetActorByEntityIdRequest{
		EntityId: userRes.GetUser().GetId(),
		Type:     types.Actor_USER,
	})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		if actorRes == nil || !actorRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error getting actor by user ID", zap.Error(te))
		}
		return
	}

	isFrozen, accountFreezeStatus, err := s.isAccountFrozen(ctx, actorRes.GetActor().GetId())
	if err != nil {
		logger.Error(ctx, "failed to check account frozen status", zap.Error(err), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return
	}

	if !isFrozen {
		return
	}

	err = s.updateTicketGroupToRiskOps(ctx, &ticket.Ticket{
		Id:      req.GetTicketId(),
		ActorId: actorRes.GetActor().GetId(),
	}, accountFreezeStatus)
	if err != nil {
		logger.Error(ctx, "error while updating ticket group to RiskOps for email source", zap.Error(err), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
	}
}

func (s *Service) isAccountFrozen(ctx context.Context, actorId string) (bool, accountEnumsPb.FreezeStatus, error) {
	// Check if account is frozen
	accountStatus, accountStatusErr := s.accountStatusFetcher.FetchAccountStatusForActor(ctx, actorId, enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME, accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED)
	if accountStatusErr != nil {
		logger.Error(ctx, "failed to fetch account status", zap.String("actorId", actorId), zap.Error(accountStatusErr))
		return false, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, accountStatusErr
	}

	if accountStatus == nil {
		logger.Error(ctx, "account status is nil", zap.String("actorId", actorId))
		return false, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, fmt.Errorf("account status is nil for actor %s", actorId)
	}

	// Check if user is on any type of freeze
	isTotalFreeze := accountStatus.FreezeStatus == accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE
	isCreditFreeze := accountStatus.FreezeStatus == accountEnumsPb.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE
	isDebitFreeze := accountStatus.FreezeStatus == accountEnumsPb.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE

	return isTotalFreeze || isCreditFreeze || isDebitFreeze, accountStatus.FreezeStatus, nil
}

func (s *Service) updateTicketGroupToRiskOps(ctx context.Context, cxTicket *ticket.Ticket, accountFreezeStatus accountEnumsPb.FreezeStatus) error {
	// Storing original group
	originalGroup := cxTicket.Group.String()

	logger.Info(ctx, "updating ticket group to RiskOps", zap.Int64("ticketId", cxTicket.Id), zap.String("originalGroup", originalGroup), zap.String("actorId", cxTicket.GetActorId()))

	// Update ticket group to RiskOps first
	cxTicket.Group = ticket.Group_GROUP_RISK_OPS
	groupId, ok := s.conf.SupportTicketFreshdeskConfig.GroupEnumToGroupIdMapping[cxTicket.Group.String()]
	if ok {
		cxTicket.GroupId = groupId
	}

	// Update the ticket in Freshdesk
	resp, err := s.fdClient.UpdateTicket(ctx, &freshdeskPb.UpdateTicketRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		Ticket: &freshdeskPb.Ticket{
			Id:    cxTicket.Id,
			Group: freshdeskPb.Group_GROUP_RISK_OPS,
		},
	})
	if err != nil {
		return fmt.Errorf("failed to update ticket group to RiskOps: %w", epifigrpc.RPCError(resp, err))
	}

	// Event tracking
	event := cxEvents.NewTicketReroutedEvent(cxTicket.GetActorId(), cxTicket.Id, accountFreezeStatus, originalGroup)
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), event)
	})

	logger.Info(ctx, "successfully updated ticket group to RiskOps", zap.Int64("ticketId", cxTicket.Id), zap.String("originalGroup", originalGroup), zap.String("actorId", cxTicket.GetActorId()))

	return nil
}

func (s *Service) FetchAccessTokenForNuggetChatbot(ctx context.Context, req *chatPb.FetchAccessTokenForNuggetChatbotRequest) (*chatPb.FetchAccessTokenForNuggetChatbotResponse, error) {
	actorId := req.GetActorId()

	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})

	if te := epifigrpc.RPCError(userRes, err); te != nil {
		cxLogger.Error(ctx, "error while fetching user profile", zap.Error(te), zap.String(logger.ACTOR_ID, actorId))
		return &chatPb.FetchAccessTokenForNuggetChatbotResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching user profile"),
		}, nil
	}

	// fetch mapping from vendor mapping service
	resp, err := s.vendorMappingClient.GetBEMappingById(ctx, &vendormapping.GetBEMappingByIdRequest{
		Id: actorId,
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching mapping from vendor mapping service", zap.Error(err))
		return &chatPb.FetchAccessTokenForNuggetChatbotResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching mapping from vendor mapping service"),
		}, nil
	}

	fetchAccessTokenReq := &nugget.FetchAccessTokenRequest{
		Header:      &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ZOMATO},
		ActorId:     resp.GetZomatoId(),
		Platform:    epificontext.AppPlatformFromContext(ctx),
		Name:        userRes.GetUser().GetProfile().GetKycName(),
		Email:       userRes.GetUser().GetProfile().GetEmail(),
		PhoneNumber: userRes.GetUser().GetProfile().GetPhoneNumber(),
	}

	nuggetResp, err := s.vgNuggetClient.FetchAccessToken(ctx, fetchAccessTokenReq)
	if te := epifigrpc.RPCError(nuggetResp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching access token from nugget", zap.Error(te))
		return &chatPb.FetchAccessTokenForNuggetChatbotResponse{
			Status: rpcPb.StatusFromErrorWithDefaultInternal(fmt.Errorf("error while fetching access token from nugget. %w", te)),
		}, nil
	}

	return &chatPb.FetchAccessTokenForNuggetChatbotResponse{
		Status:      rpcPb.StatusOk(),
		AccessToken: nuggetResp.GetAccessToken(),
	}, nil
}
